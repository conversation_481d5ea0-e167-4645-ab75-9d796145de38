import asyncio
import logging
import traceback
from typing import Any, Dict, List, Optional

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.scenarios.medical_terms import extract_medical_terms
from acva_ai.models.medical_term import MedicalTerm
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.services.rag import EmbeddingService, QdrantService, RAGService
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

CHECK_LIST_OF_MEDICAL_TERMS_PROMPT = """
    This is a context in Romana from a medical visit, generated by a speech-to-text system that may contain transcription errors such as misspelled or phonetically similar words.

    Your task is to identify and extract the correct medication name, choosing the most contextually appropriate match from the provided list of candidate medications. Use both the surrounding context {context} and semantic similarity to guide your choice.

    The candidate medical terms with their relevance scores from a RAG-based retrieval system are provided in JSON format:
    {medical_terms_json}

    The JSON format is: {{"medical_term_name": relevance_score, ...}}

    Use the RAG scores as a *secondary signal* — they may influence your decision, but the *primary criterion* should be the context and the candidate medications

    Respond with:
    - The **medication name**: the most likely correct medication name from the list.
    - In **Romana**, without any commentary, explanation, or formatting.

    If no candidate fits the context with sufficient confidence, return an empty string in this format "".
"""


async def verify_medical_term_with_rag(
    medical_term_name: str,
    medical_term_context: str,
    rag_service: RAGService,
    llm_orchestrator: LLMOrchestrator,
) -> str:
    """
    Verify if a medical term name corresponds to its context using the RAG system.

    Args:
        medical_term_name: The name of the medical term
        medical_term_context: The context in which the medical term was mentioned
        rag_service: RAG service instance for terminology lookup
        llm_orchestrator: LLM orchestrator for making LLM calls

    Returns:
        Verified medical term name as string. Empty string if not verified (can be treated as boolean).
    """
    # If no medical term name, can't verify
    if not medical_term_name:
        return ""

    # Search for the medical term name in the medical terminology
    rag_results = await rag_service.query(
        collection_name="medical_terms_dictionary",
        query_text=medical_term_name,
        limit=10,
        score_threshold=0.4,
    )

    # Create JSON object with medical term names as keys and scores as values
    medical_terms_json = {
        r.get("payload", {}).get("term", ""): r.get("score", 0)
        for r in rag_results
        if r.get("payload", {}).get("term", "")  # Only include non-empty terms
    }

    prompt = CHECK_LIST_OF_MEDICAL_TERMS_PROMPT.format(
        context=medical_term_context,
        medical_terms_json=str(medical_terms_json),
    )
    llm_response = await llm_orchestrator.call_llm(prompt=prompt, response_usage=None)
    if not llm_response:
        return ""

    return llm_response


async def search_transcript_chunks(
    transcript: str, rag_service: RAGService, chunk_size: int = 5
) -> List[Dict[str, Any]]:
    """
    Search chunks of the transcript in the medical terminology RAG.

    Args:
        transcript: The full transcript text
        rag_service: RAG service instance
        chunk_size: Number of words per chunk (default: 5)

    Returns:
        List of potential medical terms found in the transcript
    """
    # Split transcript into sentences
    import re

    sentences = re.split(r"[.!?]+", transcript)

    # Process sentences in parallel to create chunks
    def process_sentence(sentence: str) -> List[str]:
        """Process a single sentence into chunks."""
        sentence = sentence.strip()
        if not sentence:
            return []

        words = sentence.split()

        # If sentence has chunk_size or fewer words, keep as is
        if len(words) <= chunk_size:
            return [sentence]

        # Create chunks of exactly chunk_size words
        return [
            " ".join(words[i : i + chunk_size])
            for i in range(0, len(words), chunk_size)
            if words[i : i + chunk_size]  # Only non-empty chunks
        ]

    # Process all sentences in parallel and flatten results
    async def process_all_sentences():
        loop = asyncio.get_event_loop()
        tasks = [
            loop.run_in_executor(None, process_sentence, sentence)
            for sentence in sentences
        ]
        results = await asyncio.gather(*tasks)
        # Flatten list of lists into single list
        return [chunk for sentence_chunks in results for chunk in sentence_chunks]

    chunks = await process_all_sentences()

    # Search each chunk in parallel
    async def search_chunk(chunk):
        return await rag_service.query(
            collection_name="medical_terms_dictionary",
            query_text=chunk,
            limit=10,
            score_threshold=0.4,
        )

    search_tasks = [search_chunk(chunk) for chunk in chunks]
    all_results = await asyncio.gather(*search_tasks)

    # Flatten and deduplicate results
    seen_terms = set()
    unique_results = []

    for results in all_results:
        for result in results:
            term = result.get("payload", {}).get("term", "")
            if term and term not in seen_terms:
                seen_terms.add(term)
                result["chunk_context"] = chunks[all_results.index(results)]
                unique_results.append(result)

    return unique_results


async def verify_medical_terms(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> List[MedicalTerm]:
    """
    Extract and verify medical terms from a transcript.

    Args:
        transcript: The medical transcript text
        response_usage: Optional usage tracking
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        List of verified MedicalTerm objects
    """
    # Initialize RAG services
    qdrant_service = QdrantService()
    embedding_service = EmbeddingService()
    rag_service = RAGService(qdrant_service, embedding_service)

    # Extract initial medical terms
    extracted_medical_terms = await extract_medical_terms(
        transcript=transcript,
        llm_orchestrator=llm_orchestrator,
        response_usage=response_usage,
    )

    # Search for additional medical terms using moving window approach
    additional_terms = await search_transcript_chunks(transcript, rag_service)

    # Process extracted medical terms
    medical_terms_list = []
    for medical_term_name, medical_term_context in extracted_medical_terms:
        # Create medical term object
        if medical_term_name or medical_term_context:
            rag_result = await verify_medical_term_with_rag(
                medical_term_name, medical_term_context, rag_service, llm_orchestrator
            )

            # Extract verified name from RAG result if verification was successful
            verified_name = None
            verification_source = "openai_initial_text"  # Default source

            if rag_result:
                verified_name = rag_result.strip()
                if verified_name == "":
                    verified_name = None
                else:
                    verification_source = "RAG"  # RAG verification was successful

            medical_term = MedicalTerm(
                medical_term_name=verified_name if verified_name else medical_term_name,
                medical_term_context=medical_term_context,
                verification_source=verification_source,
            )
            medical_terms_list.append(medical_term)

    # Add additional medical terms found through chunk searching
    for term_info in additional_terms:
        # Check if this term is already in our list
        term = term_info.get("payload", {}).get("term", "")
        if term and not any(
            a.medical_term_name.lower() == term.lower() for a in medical_terms_list
        ):
            chunk_context = term_info.get("chunk_context", "")
            medical_term = MedicalTerm(
                medical_term_name=term,
                medical_term_context=chunk_context,
                verification_source="RAG", 
            )
            medical_terms_list.append(medical_term)

    return medical_terms_list


async def process_medical_terms(
    task_id: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
):
    """
    Process medical terms from the transcript

    Args:
        task_id: Unique identifier for the task
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    try:
        logger.info(f"[Task {task_id}] Starting medical terms processing")
        processing_status.start_stage("medical_terms_processing")
        mongo_instance.update_processing_status(task_id, processing_status.dict())
        transcript = (
            visit_report.transcript
            if visit_report.transcript
            else visit_report.raw_transcript
        )

        if not transcript:
            logger.warning(
                f"[Task {task_id}] No transcript available for medical terms processing"
            )
            processing_status.complete_stage("medical_terms_verification", [])
            return

        medical_terms = await verify_medical_terms(
            transcript, llm_orchestrator=llm_orchestrator, response_usage=response_usage
        )
        visit_report.medical_terms = medical_terms
        processing_status.complete_stage("medical_terms_verification", medical_terms)
        logger.info(f"[Task {task_id}] Completed medical terms verification")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("medical_terms_verification", e, stack_trace)
        logger.error(
            f"[Task {task_id}] Error verifying medical terms: {e}\n{stack_trace}"
        )
    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
