import logging
import os
import tempfile
import uuid
from typing import List, Optional

from fastapi import APIRouter, Body, Depends, File, HTTPException, Query, UploadFile
from fastapi.responses import JSONResponse

from acva_ai._params import DEFAULT_LLM_PROVIDER, DEFAULT_TRANSCRIPT_PROVIDER
from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator, LLMProvider
from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.llm.scenarios.affections import extract_affections
from acva_ai.llm.scenarios.grammar import _grammar_check_by_chunks
from acva_ai.llm.transcript_orchestrator import TranscriptOrchestrator
from acva_ai.llm.transcript_providers.transcript_provider import TranscriptProvider
from acva_ai.models.affection import Affection
from acva_ai.models.visit_report import VisitReport
from acva_ai.pipeline.affections_processing import (
    search_transcript_chunks,
    verify_affections,
)
from acva_ai.pipeline.domain_insights import _process_domain_insights
from acva_ai.pipeline.medical_report import build_medical_report
from acva_ai.pipeline.medication_processing import _process_medication
from acva_ai.services.rag import rag_service
from acva_ai.utils.audio_utils import concatenate_audio_files, normalize_audio_segment
from acva_ai.utils.general_utils import compute_levenshtein_similarities
from acva_ai.utils.security_service import SecurityService
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

methods_router = APIRouter(
    tags=["Methods"], dependencies=[Depends(SecurityService.get_api_key)]
)


# Transcription routes
@methods_router.post("/transcription/transcribe")
async def transcribe_audio(
    files: List[UploadFile] = File(...),
    transcript_provider: TranscriptProvider = Query(
        DEFAULT_TRANSCRIPT_PROVIDER,
        description="Transcript provider to use (openai, elevenlabs, azure)",
    ),
    diarize: bool = Query(
        False,
        description="Enable speaker diarization (only supported by ElevenLabs)",
    ),
):
    """
    Transcribes an audio file and returns the raw transcript.

    This endpoint handles the first step of the medical transcription pipeline:
    converting speech to text with confidence scores.

    - **file**: Audio file to transcribe (MP3, WAV, M4A formats supported)

    Returns a JSON object containing the `task_id` and `transcript`.
    """
    try:
        # Validate diarization parameter
        if diarize and transcript_provider.value != "elevenlabs":
            raise HTTPException(
                status_code=400,
                detail=f"Speaker diarization is not supported by {transcript_provider.value} provider. "
                "Please use ElevenLabs provider for speaker diarization.",
            )

        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create temporary directory if it doesn't exist
        temp_dir = os.path.join(tempfile.gettempdir(), "acva_ai")
        os.makedirs(temp_dir, exist_ok=True)

        # Concatenate multiple files into a single AudioSegment
        audio_segment = concatenate_audio_files(files)
        audio_segment = normalize_audio_segment(audio_segment)

        # Save the audio segment to a temporary file
        file_path = os.path.join(temp_dir, f"{task_id}.wav")
        audio_segment.export(file_path, format="wav")

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Create transcript orchestrator
        orchestrator = TranscriptOrchestrator(
            primary_provider=transcript_provider,
        )

        # Transcribe the audio with optional diarization
        if diarize:
            transcript_response = await orchestrator.transcribe_with_diarization(
                audio_segment=audio_segment,
                response_usage=response_usage,
                diarize=diarize,
            )
            # Handle different response types from providers
            if hasattr(transcript_response, "to_dict"):
                transcript_result = transcript_response.to_dict()
            elif isinstance(transcript_response, str):
                # Fallback for providers that don't support TranscriptionResponse yet
                transcript_result = {"text": transcript_response, "speakers_data": None}
            else:
                # Fallback for any other type
                transcript_result = {
                    "text": str(transcript_response),
                    "speakers_data": None,
                }
        else:
            transcript_tuple = await orchestrator.generate_transcription(
                audio_segment=audio_segment, response_usage=response_usage
            )
            transcript_text = (
                transcript_tuple[0]
                if isinstance(transcript_tuple, tuple)
                else transcript_tuple
            )
            transcript_result = {"text": transcript_text, "speakers_data": None}

        return JSONResponse({"task_id": task_id, "transcript": transcript_result})
    except Exception as e:
        logger.error(f"Error in transcribe_audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Grammar routes
@methods_router.post("/grammar/correct-grammar")
async def correct_grammar_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Corrects grammar and improves the quality of a transcript.

    This endpoint handles the second step of the medical transcription pipeline:
    correcting grammar, fixing typos, and improving the overall quality of the transcript.

    - **transcript**: Transcript text to correct
    - **provider**: LLM provider to use (openai, azure)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()
        orchestrator = LLMOrchestrator(llm_provider)

        # Correct grammar
        corrected_transcript, explanations = await _grammar_check_by_chunks(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        return JSONResponse(
            {"task_id": task_id, "corrected_transcript": corrected_transcript}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in correct_grammar_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Medication routes
@methods_router.post("/medication/extract-medications")
async def extract_medications(
    transcript: str = Body(..., media_type="text/plain"),
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Extracts medication information from a transcript.

    This endpoint handles the third step of the medical transcription pipeline:
    identifying medications mentioned in the transcript along with their context,
    and enriching them with additional information from medical databases.

    - **transcript**: Transcript text to analyze for medications
    - **provider**: LLM provider to use (openai, azure)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()
        orchestrator = LLMOrchestrator(llm_provider)

        # Extract medications
        medications = await _process_medication(
            transcript=transcript,
            llm_orchestrator=orchestrator,
            response_usage=response_usage,
        )

        # Convert list of Medication objects to list of dictionaries
        medications_list = [med.model_dump() for med in medications]

        return JSONResponse({"task_id": task_id, "medications": medications_list})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_medications: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Medication routes
@methods_router.post("/medication/extract-medications-rag-only")
async def extract_medications_rag_only(
    text: str = Body(..., media_type="text/plain"),
    limit: int = 20,
    score_threshold: float = 0.5,
):
    """
    Extracts medication information from a transcript using RAG only.

    This endpoint handles the third step of the medical transcription pipeline:
    identifying medications mentioned in the transcript along with their context,
    and enriching them with additional information from medical databases.

    - **transcript**: Transcript text to analyze for medications
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Extract candidate medications from RAG
        rag_results = await rag_service.query(
            collection_name="nomenclatura",
            query_text=text,
            limit=limit,
            score_threshold=score_threshold,
        )

        # Check if we have any results
        if not rag_results:
            logger.warning(f"No RAG results found for query: {text}")
            return JSONResponse({"task_id": task_id, "medications": []})

        # Use utility function to compute Levenshtein similarities and score results
        scored_results = compute_levenshtein_similarities(
            query_text=text,
            rag_results=rag_results,
            weight_embedding=0.2,
            weight_levenshtein=0.8,
        )

        ranked_results = sorted(scored_results, key=lambda r: r["score"], reverse=True)

        return JSONResponse({"task_id": task_id, "medications": ranked_results})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_medications_rag_only: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@methods_router.post("/affection/extract-affection-rag")
async def extract_affection_rag(
    transcript: str = Body(..., media_type="text/plain"),
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Extracts affection information from a transcript using RAG only.

    This endpoint handles the third step of the medical transcription pipeline:
    identifying affection mentioned in the transcript along with their context,
    and enriching them with additional information from medical databases.

    - **transcript**: Transcript text to analyze for affection
    """
    try:
        # Create a unique task ID
        affections_list = []
        llm_orchestrator = LLMOrchestrator(llm_provider)
        extracted_affections = await extract_affections(
            transcript=transcript, llm_orchestrator=llm_orchestrator
        )
        for affection_name, affection_context in extracted_affections:
            if affection_name or affection_context:
                affection = Affection(
                    affection_name=affection_name, affection_context=affection_context
                )
                affections_list.append(affection)

        affections_list = [affection.model_dump() for affection in affections_list]

        return JSONResponse({"affections": affections_list})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_medications_rag_only: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Affection routes
@methods_router.post("/affection/extract-affections")
async def extract_affections_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Extracts medical conditions and affections from a transcript.

    This endpoint handles the fourth step of the medical transcription pipeline:
    identifying medical conditions, symptoms, and diagnoses mentioned in the transcript,
    and verifying them against medical terminology databases.

    - **transcript**: Transcript text to analyze for medical conditions
    - **provider**: LLM provider to use (openai, azure)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()
        orchestrator = LLMOrchestrator(llm_provider)

        # Extract affections
        affections = await verify_affections(
            transcript=transcript,
            llm_orchestrator=orchestrator,
            response_usage=response_usage,
        )

        # Convert list of Affection objects to list of dictionaries
        affections_list = [affection.model_dump() for affection in affections]

        return JSONResponse({"task_id": task_id, "affections": affections_list})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_affections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Medical note routes
@methods_router.post("/medical-report/generate-medical-note")
async def generate_medical_note_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Generates a structured medical note from a transcript.

    This endpoint handles the fifth step of the medical transcription pipeline:
    generating a structured medical note with sections like chief complaint,
    history of present illness, medications, etc.

    - **transcript**: Transcript text to generate a medical note from
    - **provider**: LLM provider to use (openai, azure)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()
        orchestrator = LLMOrchestrator(llm_provider)

        # Generate medical note
        medical_note = await build_medical_report(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        return JSONResponse({"task_id": task_id, "medical_note": medical_note})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_medical_note_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Domain insight routes
@methods_router.post("/domain/extract-domain-insights")
async def extract_domain_insights_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    task_id: Optional[str] = None,
    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    ),
):
    """
    Extracts domain-specific insights from a transcript.

    This endpoint handles the sixth step of the medical transcription pipeline:
    extracting domain-specific insights like medical specialties, procedures,
    and other relevant information.

    - **transcript**: Transcript text to extract domain insights from
    - **task_id**: Optional task ID to associate with this extraction
    - **provider**: LLM provider to use (openai, azure)
    """
    try:
        # Create a unique task ID if not provided
        if not task_id:
            task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()
        try:
            visit_report = mongo_instance.get_visit_report(task_id)
        except Exception as e:
            logger.error(f"Failed to retrieve visit report: {e}")
            visit_report = VisitReport(task_id=task_id)
        orchestrator = LLMOrchestrator(llm_provider)

        # Extract domain insights
        domain_insights = await _process_domain_insights(
            task_id=task_id,
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
            visit_report=visit_report,
        )

        # Ensure domain_insights is JSON serializable
        if isinstance(domain_insights, list):
            # If it's a list of Pydantic models
            serialized_insights = [
                item.model_dump() if hasattr(item, "model_dump") else item
                for item in domain_insights
            ]
        elif hasattr(domain_insights, "model_dump"):
            # If it's a Pydantic model
            serialized_insights = domain_insights.model_dump()
        else:
            # If it's already a dict or other JSON serializable type
            serialized_insights = domain_insights

        return JSONResponse(
            {"task_id": task_id, "domain_insights": serialized_insights}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_domain_insights_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))
